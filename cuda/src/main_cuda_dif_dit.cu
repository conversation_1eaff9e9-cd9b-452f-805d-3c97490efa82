/*
 * ===========================================
 * 文件名: main_cuda_dif_dit.cu
 * 描述: CUDA DIF/DIT NTT实现
 * 目标: 实现Decimation-In-Frequency和Decimation-In-Time的CUDA版本
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_dif_dit.cu -o ntt_cuda_dif_dit
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// CUDA Kernel: DIF NTT蝶形运算
__global__ void dif_kernel(int *data, int len, const int *twiddles, int p, int n, int offset) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;

    if(block_id * len >= n) return;

    int base = block_id * len;
    if(base + local_id + half_len >= n) return;

    int g = data[base + local_id];
    int h = data[base + local_id + half_len];

    // DIF: 对下半部分先乘旋转因子，然后加减
    // 在CPU版本中，t是当前长度阶段的块索引，即block_id
    // 但在calc_powg中，旋转因子是按照特定模式存储的
    // 对于DIF，我们需要使用正确的旋转因子索引
    long long w = twiddles[block_id];
    h = (1LL * h * w) % p;

    data[base + local_id] = (g + h) % p;
    data[base + local_id + half_len] = (g - h + p) % p;
}

// CUDA Kernel: DIT NTT蝶形运算
__global__ void dit_kernel(int *data, int len, const int *twiddles, int p, int n, int offset) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;

    if(block_id * len >= n) return;

    int base = block_id * len;
    if(base + local_id + half_len >= n) return;

    int g = data[base + local_id];
    int h = data[base + local_id + half_len];

    // DIT: 先加减，对差值乘旋转因子
    data[base + local_id] = (g + h) % p;
    long long w = twiddles[block_id];  // 在CPU版本中，t是当前长度阶段的块索引，直接使用block_id
    data[base + local_id + half_len] = (1LL * (g - h + p) * w) % p;
}

// CUDA Kernel: 数组缩放
__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

// CUDA Kernel: 数组反转（DIT逆变换需要）- 反转除了第一个元素之外的所有元素
__global__ void reverse_kernel(int *data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int total_pairs = (n - 1) / 2;  // 除了第一个元素，剩下n-1个元素需要反转

    if(idx < total_pairs) {
        int left = idx + 1;  // 从第二个元素开始
        int right = n - 1 - idx;  // 对应的右边元素

        int temp = data[left];
        data[left] = data[right];
        data[right] = temp;
    }
}

// 使用CPU版本的calc_powg来预计算旋转因子
void calc_powg_cpu(int w[], int G, int P, int gen) {
    w[0] = 1;
    long long f;
    const int g = qpow(gen, (P-1)/G, P);
    for (int t = 0; (1<<(t+1)) < G; ++t) {
        f = w[1<<t] = qpow(g, G>>(t+2), P);
        for (int x = 1<<t; x < 1<<(t+1); ++x)
            w[x] = (1LL * f * w[x - (1<<t)]) % P;
    }
}

// 预计算旋转因子 - 使用CPU版本的calc_powg
void precompute_twiddles_dif(std::vector<int>& twiddles, int n, int p) {
    twiddles.resize(n);
    calc_powg_cpu(twiddles.data(), n, p, 3);
}

void precompute_twiddles_dit(std::vector<int>& twiddles, int n, int p) {
    twiddles.resize(n);
    calc_powg_cpu(twiddles.data(), n, p, 3);
}

// CUDA DIF NTT
void cuda_dif_ntt(int *h_data, int n, int p) {
    int *d_data, *d_twiddles;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles, n * sizeof(int)));

    std::vector<int> twiddles;
    precompute_twiddles_dif(twiddles, n, p);

    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles, twiddles.data(), n * sizeof(int), cudaMemcpyHostToDevice));

    for(int len = n; len > 1; len >>= 1) {
        int half_len = len >> 1;
        int num_blocks = n / len;
        int total_butterflies = num_blocks * half_len;

        int threads = min(1024, total_butterflies);
        int blocks = (total_butterflies + threads - 1) / threads;

        dif_kernel<<<blocks, threads>>>(d_data, len, d_twiddles, p, n, 0);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_twiddles));
}

// CUDA DIT INTT (逆变换)
void cuda_dit_intt(int *h_data, int n, int p) {
    int *d_data, *d_twiddles;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles, n * sizeof(int)));

    std::vector<int> twiddles;
    precompute_twiddles_dit(twiddles, n, p);

    // 对旋转因子取逆
    for(int& w : twiddles) {
        w = qpow(w, p-2, p);
    }

    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles, twiddles.data(), n * sizeof(int), cudaMemcpyHostToDevice));

    for(int len = 2; len <= n; len <<= 1) {
        int half_len = len >> 1;
        int num_blocks = n / len;
        int total_butterflies = num_blocks * half_len;

        int threads = min(1024, total_butterflies);
        int blocks = (total_butterflies + threads - 1) / threads;

        dit_kernel<<<blocks, threads>>>(d_data, len, d_twiddles, p, n, 0);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    // 缩放
    int inv_n = qpow(n, p-2, p);
    int threads = min(1024, n);
    int blocks = (n + threads - 1) / threads;
    scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_twiddles));
}

// 多项式乘法使用DIF/DIT
void cuda_poly_multiply_dif_dit(int *a, int *b, int *result, int n, int p) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    // DIF正变换
    cuda_dif_ntt(A.data(), lim, p);
    cuda_dif_ntt(B.data(), lim, p);
    
    // 点乘
    for(int i = 0; i < lim; i++) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }
    
    // DIT逆变换
    cuda_dit_intt(A.data(), lim, p);
    
    // 拷贝结果
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }
}

int main() {
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("使用GPU: %s\n", prop.name);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);
    
    int a[300000], b[300000], ab[300000];
    
    printf("\nCUDA DIF/DIT NTT 实现测试:\n");
    printf("================================================================\n");
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_dif_dit(a, b, ab, n, p);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
}
