/*
 * ===========================================
 * 文件名: main_cuda_montgomery.cu
 * 描述: CUDA Montgomery规约优化的NTT实现 (Corrected)
 * 目标: 使用Montgomery规约优化模运算性能
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_montgomery.cu -o ntt_cuda_montgomery
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

__host__ unsigned int mod_inverse_host(unsigned int a, unsigned int m) {
    long long m0 = m, t, q;
    long long x0 = 0, x1 = 1;
    long long a_ll = a, m_ll = m;
    if (m_ll == 1) return 0;
    while (a_ll > 1) {
        if (m_ll == 0) return 0; // No inverse if gcd is not 1
        q = a_ll / m_ll;
        t = m_ll;
        m_ll = a_ll % m_ll;
        a_ll = t;
        t = x0;
        x0 = x1 - q * x0;
        x1 = t;
    }
    if (a_ll != 1) return 0; // Inverse exists only if gcd is 1
    if (x1 < 0) x1 += m0;
    return (unsigned int)x1;
}

struct MontgomeryParams {
    unsigned int mod;
    unsigned int mod_prime;
    unsigned int r2_mod;

    __host__ MontgomeryParams(unsigned int m = 0) : mod(m), mod_prime(0), r2_mod(0) {
        if (m > 0) {
            unsigned long long p_inv = 2;
            for (int i = 0; i < 5; ++i) { p_inv = p_inv * (2 - m * p_inv); }
            mod_prime = (unsigned int)(-p_inv);
            r2_mod = (unsigned int)(((unsigned __int128)1 << 64) % m);
        }
    }

    __host__ __device__ inline unsigned int reduce(unsigned long long x) const {
        unsigned int q = (unsigned int)x * mod_prime;
        unsigned long long t = (unsigned long long)x + (unsigned long long)q * mod;
        unsigned int res = (unsigned int)(t >> 32);
        return (res >= mod) ? res - mod : res;
    }

    __host__ __device__ inline unsigned int to_montgomery(unsigned int x) const {
        return reduce((unsigned long long)x * r2_mod);
    }

    __host__ __device__ inline unsigned int from_montgomery(unsigned int x) const {
        return reduce(x);
    }

    __host__ __device__ inline unsigned int mul(unsigned int a, unsigned int b) const {
        return reduce((unsigned long long)a * b);
    }

    __host__ __device__ inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int sum = a + b;
        return (sum >= mod) ? sum - mod : sum;
    }

    __host__ __device__ inline unsigned int sub(unsigned int a, unsigned int b) const {
        return (a >= b) ? (a - b) : (a - b + mod);
    }
};

__global__ void bit_reverse_kernel(int *data, const int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        int target = rev[idx];
        if (idx < target) {
            int tmp = data[idx];
            data[idx] = data[target];
            data[target] = tmp;
        }
    }
}

__global__ void to_montgomery_kernel(int *data, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)mont.to_montgomery((unsigned int)data[idx]);
    }
}

__global__ void from_montgomery_kernel(int *data, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)mont.from_montgomery((unsigned int)data[idx]);
    }
}

__global__ void pointwise_mul_montgomery_kernel(int *a, const int *b, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        a[idx] = mont.mul((unsigned int)a[idx], (unsigned int)b[idx]);
    }
}

__global__ void scale_montgomery_kernel(int *data, unsigned int factor_mont, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = mont.mul((unsigned int)data[idx], factor_mont);
    }
}

__global__ void ntt_montgomery_kernel(int *data, int len, int wn_mont, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    unsigned int w = mont.to_montgomery(1);
    for(int i = 0; i < local_id; i++) {
        w = mont.mul(w, (unsigned int)wn_mont);
    }
    
    unsigned int u = (unsigned int)data[base + local_id];
    unsigned int v = mont.mul((unsigned int)data[base + local_id + half_len], w);
    
    data[base + local_id] = (int)mont.add(u, v);
    data[base + local_id + half_len] = (int)mont.sub(u, v);
}

__global__ void ntt_montgomery_precomp_kernel(int *data, const int *twiddles, int len, 
                                             MontgomeryParams mont, int n, int offset) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    unsigned int w = (unsigned int)twiddles[offset + local_id];
    unsigned int u = (unsigned int)data[base + local_id];
    unsigned int v = mont.mul((unsigned int)data[base + local_id + half_len], w);
    
    data[base + local_id] = (int)mont.add(u, v);
    data[base + local_id + half_len] = (int)mont.sub(u, v);
}

void precompute_twiddles_montgomery(std::vector<int>& twiddles, int n, int p, bool inverse, const MontgomeryParams& mont) {
    twiddles.resize(n);
    int offset = 0;
    for (int len = 2; len <= n; len <<= 1) {
        long long wn_normal = qpow(3, (p - 1) / len, p);
        if (inverse) wn_normal = qpow(wn_normal, p - 2, p);
        
        long long w_normal = 1;
        for (int i = 0; i < len / 2; i++) {
            twiddles[offset + i] = mont.to_montgomery((unsigned int)w_normal);
            w_normal = (w_normal * wn_normal) % p;
        }
        offset += len / 2;
    }
}

void cuda_ntt_montgomery(int* d_data, int n, bool inverse, int p, bool use_precomp, const MontgomeryParams& mont, int* d_rev, int* d_twiddles) {
    int threads = std::min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());

    if (use_precomp) {
        int offset = 0;
        for (int len = 2; len <= n; len <<= 1) {
            int half_len = len >> 1;
            int total_butterflies = n / len * half_len;
            threads = std::min(1024, total_butterflies);
            blocks = (total_butterflies + threads - 1) / threads;
            ntt_montgomery_precomp_kernel<<<blocks, threads>>>(d_data, d_twiddles, len, mont, n, offset);
            CHECK_CUDA(cudaDeviceSynchronize());
            offset += half_len;
        }
    } else {
        for (int len = 2; len <= n; len <<= 1) {
            long long wn_normal = qpow(3, (p - 1) / len, p);
            if (inverse) wn_normal = qpow(wn_normal, p - 2, p);
            unsigned int wn_mont = mont.to_montgomery((unsigned int)wn_normal);

            int half_len = len >> 1;
            int total_butterflies = n / len * half_len;
            threads = std::min(1024, total_butterflies);
            blocks = (total_butterflies + threads - 1) / threads;
            ntt_montgomery_kernel<<<blocks, threads>>>(d_data, len, wn_mont, mont, n);
            CHECK_CUDA(cudaDeviceSynchronize());
        }
    }
}

void cuda_poly_multiply_montgomery(int* a, int* b, int* result, int n, int p, bool use_precomp = true) {
    int lim = 1;
    while (lim < 2 * n) lim <<= 1;

    std::vector<int> A_h(lim, 0), B_h(lim, 0);
    for (int i = 0; i < n; i++) { A_h[i] = a[i]; B_h[i] = b[i]; }

    MontgomeryParams mont(p);
    std::vector<int> h_rev(lim);
    int lg = 0;
    while ((1 << lg) < lim) lg++;
    for (int i = 0; i < lim; i++) { h_rev[i] = (h_rev[i >> 1] >> 1) | ((i & 1) << (lg - 1)); }

    int *d_A, *d_B, *d_rev, *d_twiddles = nullptr;
    CHECK_CUDA(cudaMalloc(&d_A, lim * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_B, lim * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, lim * sizeof(int)));

    CHECK_CUDA(cudaMemcpy(d_A, A_h.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_B, B_h.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, h_rev.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    
    int threads = std::min(1024, lim);
    int blocks = (lim + threads - 1) / threads;

    to_montgomery_kernel<<<blocks, threads>>>(d_A, mont, lim);
    to_montgomery_kernel<<<blocks, threads>>>(d_B, mont, lim);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    if (use_precomp) {
        std::vector<int> h_twiddles;
        precompute_twiddles_montgomery(h_twiddles, lim, p, false, mont);
        CHECK_CUDA(cudaMalloc(&d_twiddles, lim * sizeof(int)));
        CHECK_CUDA(cudaMemcpy(d_twiddles, h_twiddles.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    }
    
    cuda_ntt_montgomery(d_A, lim, false, p, use_precomp, mont, d_rev, d_twiddles);
    cuda_ntt_montgomery(d_B, lim, false, p, use_precomp, mont, d_rev, d_twiddles);
    
    pointwise_mul_montgomery_kernel<<<blocks, threads>>>(d_A, d_B, mont, lim);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    if (use_precomp) {
        CHECK_CUDA(cudaFree(d_twiddles));
        d_twiddles = nullptr;
        std::vector<int> h_twiddles;
        precompute_twiddles_montgomery(h_twiddles, lim, p, true, mont);
        CHECK_CUDA(cudaMalloc(&d_twiddles, lim * sizeof(int)));
        CHECK_CUDA(cudaMemcpy(d_twiddles, h_twiddles.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    }
    
    cuda_ntt_montgomery(d_A, lim, true, p, use_precomp, mont, d_rev, d_twiddles);

    unsigned int inv_n_mont = mont.to_montgomery(mod_inverse_host(lim, p));
    scale_montgomery_kernel<<<blocks, threads>>>(d_A, inv_n_mont, mont, lim);
    CHECK_CUDA(cudaDeviceSynchronize());

    from_montgomery_kernel<<<blocks, threads>>>(d_A, mont, lim);
    CHECK_CUDA(cudaDeviceSynchronize());

    std::vector<int> res_h(lim);
    CHECK_CUDA(cudaMemcpy(res_h.data(), d_A, lim * sizeof(int), cudaMemcpyDeviceToHost));
    for (int i = 0; i < 2 * n - 1; i++) { result[i] = res_h[i]; }

    CHECK_CUDA(cudaFree(d_A));
    CHECK_CUDA(cudaFree(d_B));
    CHECK_CUDA(cudaFree(d_rev));
    if (d_twiddles) CHECK_CUDA(cudaFree(d_twiddles));
}

int main() {
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("使用GPU: %s\n", prop.name);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);
    
    int a[300000], b[300000], ab[300000];
    
    printf("\nCUDA Montgomery规约优化 NTT 实现测试:\n");
    printf("================================================================\n");
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        // 测试基础版本
        memset(ab, 0, sizeof(ab));
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_montgomery(a, b, ab, n, p, false);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_basic = std::chrono::duration<double, std::milli>(end - start).count();
        printf("Montgomery基础版本执行时间: %.3f ms\n", time_basic);
        
        // 测试预计算版本
        memset(ab, 0, sizeof(ab));
        start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_montgomery(a, b, ab, n, p, true);
        end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_precomp = std::chrono::duration<double, std::milli>(end - start).count();
        printf("Montgomery预计算版本执行时间: %.3f ms\n", time_precomp);
        printf("加速比: %.2fx\n", time_basic / time_precomp);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
}
